import { BaseErrorCode } from '../../types-global/BaseErrorCode';

export class McpError extends <PERSON>rror {
  public readonly code: BaseErrorCode;
  public readonly context?: Record<string, unknown>;

  constructor(code: BaseErrorCode, message: string, context?: Record<string, unknown>) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.context = context;
    Error.captureStackTrace(this, this.constructor);
  }
}

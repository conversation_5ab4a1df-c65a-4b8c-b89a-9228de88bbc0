import winston from 'winston';
import config from '../../config/index.js';

const { combine, timestamp, json, colorize, align, printf } = winston.format;

const logger = winston.createLogger({
  level: config.NODE_ENV === 'development' ? 'debug' : 'info',
  format: combine(
    timestamp({
      format: 'YYYY-MM-DD HH:mm:ss',
    }),
    json()
  ),
  transports: [
    new winston.transports.Console({
      format: combine(
        colorize(),
        align(),
        printf((info) => `[${info.timestamp}] ${info.level}: ${info.message}`)
      ),
    }),
  ],
});

export default logger;

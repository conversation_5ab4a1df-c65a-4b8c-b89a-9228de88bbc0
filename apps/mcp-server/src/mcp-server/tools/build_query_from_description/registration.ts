import { McpServer } from '@modelcontextprotocol/sdk';
import {
  buildQueryFromDescriptionInputSchema,
  buildQueryFromDescriptionOutputSchema,
  buildQueryFromDescriptionLogic,
} from './logic';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/internal/errorHandler';
import { DataContract } from 'data-contract';

export function registerBuildQueryFromDescription(server: McpServer, contract: DataContract) {
  server.tool(
    'build_query_from_description',
    'Builds a SQL query from a natural language description.',
    async (c: any) => {
      try {
        const input = await c.req.json();
        const validatedInput = buildQueryFromDescriptionInputSchema.parse(input);
        const result = await buildQueryFromDescriptionLogic(contract, validatedInput);
        const validatedOutput = buildQueryFromDescriptionOutputSchema.parse(result);
        return c.json(validatedOutput);
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return c.json({ error: handledError }, 400);
      }
    }
  );
}

import { z } from 'zod';
import { McpError } from '../../../utils/internal/mcpError';
import { BaseErrorCode } from '../../../types-global/BaseErrorCode';
import { DataContract } from 'data-contract';

export const exploreTableStructureInputSchema = z.object({
  tableName: z.string(),
});

// The output schema is dynamic, so we can't define a strict Zod schema for it.
// We'll just use z.any() for now. A better approach would be to define a
// generic table schema and use that.
export const exploreTableStructureOutputSchema = z.any();

export async function exploreTableStructureLogic(
  contract: DataContract,
  input: z.infer<typeof exploreTableStructureInputSchema>
): Promise<any> {
  const table = contract.tables[input.tableName];
  if (!table) {
    throw new McpError(BaseErrorCode.NOT_FOUND, `Table not found: ${input.tableName}`);
  }
  // Return a copy to avoid callers modifying the original contract object
  return { ...table };
}

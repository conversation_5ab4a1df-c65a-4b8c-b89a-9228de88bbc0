import { McpServer } from '@modelcontextprotocol/sdk';
import {
  exploreTableStructureInputSchema,
  exploreTableStructureOutputSchema,
  exploreTableStructureLogic,
} from './logic.js';
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';

export function registerExploreTableStructure(server: McpServer, contract: DataContract) {
  server.tool(
    'explore_table_structure',
    'Explores the structure of a table, returning its columns and their properties.',
    async (c: any) => {
      try {
        const input = await c.req.json();
        const validatedInput = exploreTableStructureInputSchema.parse(input);
        const result = await exploreTableStructureLogic(contract, validatedInput);
        const validatedOutput = exploreTableStructureOutputSchema.parse(result);
        return c.json(validatedOutput);
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return c.json({ error: handledError }, 400);
      }
    }
  );
}

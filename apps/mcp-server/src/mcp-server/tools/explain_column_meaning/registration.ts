import { McpServer } from '@modelcontextprotocol/sdk';
import {
  explainColumnMeaningInputSchema,
  explainColumnMeaningOutputSchema,
  explainColumnMeaningLogic,
} from './logic.js';
import { <PERSON>rror<PERSON><PERSON><PERSON> } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';

export function registerExplainColumnMeaning(server: McpServer, contract: DataContract) {
  server.tool(
    'explain_column_meaning',
    'Explains the meaning of a column, including its business name, description, and business rules.',
    async (c: any) => {
      try {
        const input = await c.req.json();
        const validatedInput = explainColumnMeaningInputSchema.parse(input);
        const result = await explainColumnMeaningLogic(contract, validatedInput);
        const validatedOutput = explainColumnMeaningOutputSchema.parse(result);
        return c.json(validatedOutput);
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return c.json({ error: handledError }, 400);
      }
    }
  );
}

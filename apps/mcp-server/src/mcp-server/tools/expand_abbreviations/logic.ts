import { z } from 'zod';
import { McpError } from '../../../utils/internal/mcpError';
import { BaseErrorCode } from '../../../types-global/BaseErrorCode';
import { DataContract } from 'data-contract';

export const expandAbbreviationsInputSchema = z.object({
  abbreviation: z.string(),
});

export const expandAbbreviationsOutputSchema = z.object({
  expansion: z.string(),
});

export async function expandAbbreviationsLogic(
  contract: DataContract,
  input: z.infer<typeof expandAbbreviationsInputSchema>
): Promise<z.infer<typeof expandAbbreviationsOutputSchema>> {
  const expansion = contract.abbreviations?.[input.abbreviation];
  if (!expansion) {
    throw new McpError(BaseErrorCode.NOT_FOUND, `Abbreviation not found: ${input.abbreviation}`);
  }
  return { expansion };
}

import { McpServer } from '@modelcontextprotocol/sdk';
import {
  expandAbbreviationsInputSchema,
  expandAbbreviationsOutputSchema,
  expandAbbreviationsLogic,
} from './logic';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/internal/errorHandler';
import { DataContract } from 'data-contract';

export function registerExpandAbbreviations(server: McpServer, contract: DataContract) {
  server.tool(
    'expand_abbreviations',
    'Expands an abbreviation to its full meaning.',
    async (c: any) => {
      try {
        const input = await c.req.json();
        const validatedInput = expandAbbreviationsInputSchema.parse(input);
        const result = await expandAbbreviationsLogic(contract, validatedInput);
        const validatedOutput = expandAbbreviationsOutputSchema.parse(result);
        return c.json(validatedOutput);
      } catch (error) {
        const handledError = ErrorHandler.handle(error as Error);
        return c.json({ error: handledError }, 400);
      }
    }
  );
}

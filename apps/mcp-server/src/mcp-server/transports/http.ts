import { Hono } from 'hono';
import { McpServer } from '@modelcontextprotocol/sdk';

export function httpTransport(app: Hono, server: McpServer) {
  app.post('/mcp', async (c) => {
    const mcpRequest = await c.req.json();
    const mcpResponse = await server.process(mcpRequest);
    return c.json(mcpResponse);
  });

  // The template mentions a streamable HTTP transport.
  // The MCP SDK's McpServer should handle streaming automatically if the client requests it.
  // The `server.process` method should be able to handle both unary and streaming requests.
  // We'll assume this is sufficient for now.
}

{"name": "mcp-server", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --traceResolution", "postbuild": "tsx scripts/make-executable.ts dist/index.js", "start": "node dist/index.js", "start:stdio": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=stdio node dist/index.js", "start:http": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=http node dist/index.js", "dev": "tsx --watch src/index.ts", "dev:stdio": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=stdio tsx --watch src/index.ts", "dev:http": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=http tsx --watch src/index.ts", "rebuild": "tsx scripts/clean.ts && npm run build", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@hono/node-server": "^1.11.1", "@hono/zod-validator": "^0.7.2", "@modelcontextprotocol/sdk": "^1.17.1", "@opentelemetry/instrumentation-winston": "^0.48.0", "axios": "^1.11.0", "chrono-node": "^2.8.0", "dotenv": "^16.6.1", "hono": "^4.5.0", "ioredis": "^5.4.1", "js-yaml": "^4.1.0", "pg": "^8.16.3", "data-contract": "file:../../packages/data-contract", "reflect-metadata": "^0.2.2", "sanitize-html": "^2.17.0", "winston": "^3.17.0", "winston-transport": "^4.9.0", "zod": "3.25.76"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/exporter-metrics-otlp-http": "^0.203.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.36.0", "@types/js-yaml": "^4.0.9", "@types/node": "^24.1.0", "@types/pg": "^8.15.5", "@types/sanitize-html": "^2.16.0", "@vitest/coverage-v8": "^3.2.4", "msw": "^2.10.4", "prettier": "^3.6.2", "tsx": "^4.20.3", "typedoc": "^0.28.8", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}